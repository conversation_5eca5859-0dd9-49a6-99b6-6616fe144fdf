/**
 * Comprehensive Stripe Test Data Ingestion Script (v3)
 *
 * This script is designed to populate a Stripe test account with realistic,
 * time-based subscription data for backfilling and analytics testing.
 *
 * Key Features:
 * - SAFETY CHECKS: Prevents execution on live or production environments.
 * - Cleanup: Deletes existing test data to ensure a clean slate.
 * - Test Clocks: Uses Stripe Test Clocks to simulate the passage of time
 * for accurate event generation (invoices, subscriptions, webhooks).
 * - Historical Simulation: Starts the simulation at the test clock's
 * frozen time, allowing for the creation of historical data.
 * - Lifecycle Events: Simulates customer sign-ups, churn, upgrades,
 * downgrades, and refunds over time.
 * - Pagination: Correctly handles pagination when cleaning up existing
 * resources to ensure all data is removed.
 *
 * Usage:
 * - Ensure your environment variables are set: STRIPE_SECRET_KEY, etc.
 * - Run the script with an Inngest event: `stripe/populate.test.data`
 *
 * Example Event Data:
 * {
 * "testClockId": "clock_1Oq4p4RjW2XmC61y0Zk8j3zF",
 * "initialNumberOfCustomers": 50,
 * "orgIntegrationId": "your_integration_id"
 * }
 */

import Stripe from 'stripe';
import { chunk } from '../../lib/utils';
import { inngest } from '../client';

// configurable
// number of clients
// number of weeks starting (today and backwards)
// weekly churn rate lower upper
// weekly growth rate lower upper
// weekly upgrade rate lower upper
// weekly downgrade rate lower upper
// integration id if passed, if not passed, use the main org integration

// avergae simu costs, cleanup, not a step, can be repeated, i dont care
// number of customers created
// numuber of subsc created
// number of products ? we can keep them the same? or clean them up too and recreate in the first step?
// number of upgrades downgrades is a step
// cancellations are first
// we need to add reactivations too?
// can we do it under 5 min if we move time forward in parralel?
// 1 second check for clocks instead of 2000

interface IngestTestDataParams {
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
  numberOfSteps: number;
  churnRateLower: number;
  churnRateUpper: number;
  growthRateLower: number;
  growthRateUpper: number;
  upgradeRateLower: number;
  upgradeRateUpper: number;
  downgradeRateLower: number;
  downgradeRateUpper: number;
  reactivationRateLower: number;
  reactivationRateUpper: number;
}

// Helper functions for simulation
function getRandomRate(lower: number, upper: number): number {
  return Math.random() * (upper - lower) + lower;
}

function getRandomSubset<T>(array: T[], percentage: number): T[] {
  const count = Math.floor(array.length * (percentage / 100));
  const shuffled = [...array].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}

function getRandomPrice(prices: Stripe.Price[]): Stripe.Price {
  return prices[Math.floor(Math.random() * prices.length)];
}

// TODO throttle it to 1 at the time, no concurrency
export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const {
      initialNumberOfCustomers: inc,
      numberOfSteps,
      churnRateLower,
      churnRateUpper,
      growthRateLower,
      growthRateUpper,
      upgradeRateLower,
      upgradeRateUpper,
      downgradeRateLower,
      downgradeRateUpper,
      reactivationRateLower,
      reactivationRateUpper,
    } = event.data as IngestTestDataParams;

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    console.log('Creating products...');

    // Create all the products mentioned in the code
    const basicProduct = await stripe.products.create({
      name: 'Basic Plan',
      description: 'Basic SaaS plan with essential features',
    });

    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Professional SaaS plan with advanced features',
    });

    const pricingConfig = [
      { product: basicProduct.id, interval: 'month', amount: 3600 },
      { product: basicProduct.id, interval: 'year', amount: 36000 },
      { product: proProduct.id, interval: 'month', amount: 6000 },
      { product: proProduct.id, interval: 'year', amount: 60000 },
    ];

    console.log('Creating prices...');

    const prices = await Promise.all(
      pricingConfig.map(config =>
        stripe.prices.create({
          product: config.product,
          unit_amount: config.amount,
          currency: 'usd',
          recurring: {
            interval: config.interval as 'month' | 'year',
          },
        })
      )
    );

    // Find start date based on numberOfWeeks
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - numberOfSteps * 7);
    const startTimestamp = Math.floor(startDate.getTime() / 1000);

    const createTestCustomer = async () => {
      console.time('createTestCustomer');
      const uid = crypto.randomUUID();
      const newTestClock = await stripe.testHelpers.testClocks.create({
        frozen_time: startTimestamp,
        name: `Test Clock for Customer ${uid}`,
      });
      const customerData = generateCustomerData(uid);
      const customer = await stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        description: `Test customer ${uid}`,
        test_clock: newTestClock.id,
      });
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: { token: 'tok_visa' },
      });
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customer.id,
      });
      await stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: paymentMethod.id,
        },
      });
      const randomPrice = prices[Math.floor(Math.random() * prices.length)];
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: randomPrice.id }],
        collection_method: 'charge_automatically',
        default_payment_method: paymentMethod.id,
      });
      console.timeEnd('createTestCustomer');
      return { customer, subscription, testClock: newTestClock.id };
    };

    const customers: Stripe.Customer[] = [];
    const subscriptions: Stripe.Subscription[] = [];
    const testClocks: string[] = [];

    for (const c of chunk(Array.from({ length: inc }), 5)) {
      console.time('createTestCustomerBatch');
      const batchResults = await Promise.all(c.map(createTestCustomer));
      customers.push(...batchResults.map(r => r.customer));
      subscriptions.push(...batchResults.map(r => r.subscription));
      testClocks.push(...batchResults.map(r => r.testClock));
      console.timeEnd('createTestCustomerBatch');
    }

    // Keep local copy of stripe state
    const stripeState = {
      customers: customers.map((c, i) => ({
        id: c.id,
        hasSubscription: true,
        testClockId: testClocks[i],
      })),
      subscriptions: subscriptions.map((s, i) => ({
        id: s.id,
        active: true,
        customerId: customers[i].id,
        testClockId: testClocks[i],
        currentPriceId: s.items.data[0].price.id,
      })),
      canceledSubscriptions: [] as Array<{
        id: string;
        customerId: string;
        testClockId: string;
        canceledAt: number;
      }>,
      testClocks,
      prices,
    };

    // Simulation logic - process each biweekly step
    let currentTime = startTimestamp;
    const endTime = Math.floor(Date.now() / 1000);
    const biweekInSeconds = 14 * 24 * 60 * 60; // 2 weeks in seconds
    let stepCount = 0;

    console.log(
      `Starting simulation from ${new Date(currentTime * 1000)} to ${new Date(endTime * 1000)}`
    );

    while (currentTime < endTime && stepCount < numberOfSteps) {
      console.log(
        `\n--- Step ${stepCount + 1}/${numberOfSteps} - ${new Date(currentTime * 1000)} ---`
      );

      // 1. Process cancellations/churn first
      const churnRate = getRandomRate(churnRateLower, churnRateUpper);
      const activeSubscriptions = stripeState.subscriptions.filter(
        s => s.active
      );
      const subscriptionsToCancel = getRandomSubset(
        activeSubscriptions,
        churnRate
      );

      console.log(
        `Canceling ${subscriptionsToCancel.length} subscriptions (${churnRate.toFixed(2)}% churn rate)`
      );

      for (const sub of subscriptionsToCancel) {
        try {
          await stripe.subscriptions.cancel(sub.id);
          // Update local state
          const subIndex = stripeState.subscriptions.findIndex(
            s => s.id === sub.id
          );
          if (subIndex !== -1) {
            stripeState.subscriptions[subIndex].active = false;
            stripeState.canceledSubscriptions.push({
              id: sub.id,
              customerId: sub.customerId,
              testClockId: sub.testClockId,
              canceledAt: currentTime,
            });
          }
          // Update customer state
          const customerIndex = stripeState.customers.findIndex(
            c => c.id === sub.customerId
          );
          if (customerIndex !== -1) {
            stripeState.customers[customerIndex].hasSubscription = false;
          }
        } catch (error) {
          console.error(`Failed to cancel subscription ${sub.id}:`, error);
        }
      }

      // 2. Process reactivations
      const reactivationRate = getRandomRate(
        reactivationRateLower,
        reactivationRateUpper
      );
      const customersWithoutSubscription = stripeState.customers.filter(
        c => !c.hasSubscription
      );
      const customersToReactivate = getRandomSubset(
        customersWithoutSubscription,
        reactivationRate
      );

      console.log(
        `Reactivating ${customersToReactivate.length} customers (${reactivationRate.toFixed(2)}% reactivation rate)`
      );

      for (const customer of customersToReactivate) {
        try {
          const randomPrice = getRandomPrice(prices);
          const subscription = await stripe.subscriptions.create({
            customer: customer.id,
            items: [{ price: randomPrice.id }],
            collection_method: 'charge_automatically',
          });

          // Update local state
          stripeState.subscriptions.push({
            id: subscription.id,
            active: true,
            customerId: customer.id,
            testClockId: customer.testClockId,
            currentPriceId: randomPrice.id,
          });

          const customerIndex = stripeState.customers.findIndex(
            c => c.id === customer.id
          );
          if (customerIndex !== -1) {
            stripeState.customers[customerIndex].hasSubscription = true;
          }
        } catch (error) {
          console.error(`Failed to reactivate customer ${customer.id}:`, error);
        }
      }

      // 3. Process growth (new customers)
      const growthRate = getRandomRate(growthRateLower, growthRateUpper);
      const currentActiveCustomers = stripeState.customers.filter(
        c => c.hasSubscription
      ).length;
      const newCustomersCount = Math.floor(
        currentActiveCustomers * (growthRate / 100)
      );

      console.log(
        `Adding ${newCustomersCount} new customers (${growthRate.toFixed(2)}% growth rate)`
      );

      for (let i = 0; i < newCustomersCount; i++) {
        try {
          const uid = crypto.randomUUID();
          const newTestClock = await stripe.testHelpers.testClocks.create({
            frozen_time: currentTime,
            name: `Test Clock for Customer ${uid} - Step ${stepCount + 1}`,
          });

          const customerData = generateCustomerData(uid);
          const customer = await stripe.customers.create({
            email: customerData.email,
            name: customerData.name,
            description: `Test customer ${uid} - Step ${stepCount + 1}`,
            test_clock: newTestClock.id,
          });

          const paymentMethod = await stripe.paymentMethods.create({
            type: 'card',
            card: { token: 'tok_visa' },
          });

          await stripe.paymentMethods.attach(paymentMethod.id, {
            customer: customer.id,
          });

          await stripe.customers.update(customer.id, {
            invoice_settings: {
              default_payment_method: paymentMethod.id,
            },
          });

          const randomPrice = getRandomPrice(prices);
          const subscription = await stripe.subscriptions.create({
            customer: customer.id,
            items: [{ price: randomPrice.id }],
            collection_method: 'charge_automatically',
            default_payment_method: paymentMethod.id,
          });

          // Update local state
          stripeState.customers.push({
            id: customer.id,
            hasSubscription: true,
            testClockId: newTestClock.id,
          });

          stripeState.subscriptions.push({
            id: subscription.id,
            active: true,
            customerId: customer.id,
            testClockId: newTestClock.id,
            currentPriceId: randomPrice.id,
          });

          stripeState.testClocks.push(newTestClock.id);
        } catch (error) {
          console.error(`Failed to create new customer:`, error);
        }
      }

      // 4. Process upgrades
      const upgradeRate = getRandomRate(upgradeRateLower, upgradeRateUpper);
      const currentActiveSubscriptions = stripeState.subscriptions.filter(
        s => s.active
      );
      const subscriptionsToUpgrade = getRandomSubset(
        currentActiveSubscriptions,
        upgradeRate
      );

      console.log(
        `Upgrading ${subscriptionsToUpgrade.length} subscriptions (${upgradeRate.toFixed(2)}% upgrade rate)`
      );

      for (const sub of subscriptionsToUpgrade) {
        try {
          // Find a higher-priced plan
          const currentPrice = prices.find(p => p.id === sub.currentPriceId);
          const higherPrices = prices.filter(
            p =>
              p.unit_amount &&
              currentPrice?.unit_amount &&
              p.unit_amount > currentPrice.unit_amount &&
              p.recurring?.interval === currentPrice.recurring?.interval
          );

          if (higherPrices.length > 0) {
            const newPrice = getRandomPrice(higherPrices);
            await stripe.subscriptions.update(sub.id, {
              items: [
                {
                  id: (await stripe.subscriptions.retrieve(sub.id)).items
                    .data[0].id,
                  price: newPrice.id,
                },
              ],
            });

            // Update local state
            const subIndex = stripeState.subscriptions.findIndex(
              s => s.id === sub.id
            );
            if (subIndex !== -1) {
              stripeState.subscriptions[subIndex].currentPriceId = newPrice.id;
            }
          }
        } catch (error) {
          console.error(`Failed to upgrade subscription ${sub.id}:`, error);
        }
      }

      // 5. Process downgrades
      const downgradeRate = getRandomRate(
        downgradeRateLower,
        downgradeRateUpper
      );
      const subscriptionsToDowngrade = getRandomSubset(
        currentActiveSubscriptions,
        downgradeRate
      );

      console.log(
        `Downgrading ${subscriptionsToDowngrade.length} subscriptions (${downgradeRate.toFixed(2)}% downgrade rate)`
      );

      for (const sub of subscriptionsToDowngrade) {
        try {
          // Find a lower-priced plan
          const currentPrice = prices.find(p => p.id === sub.currentPriceId);
          const lowerPrices = prices.filter(
            p =>
              p.unit_amount &&
              currentPrice?.unit_amount &&
              p.unit_amount < currentPrice.unit_amount &&
              p.recurring?.interval === currentPrice.recurring?.interval
          );

          if (lowerPrices.length > 0) {
            const newPrice = getRandomPrice(lowerPrices);
            await stripe.subscriptions.update(sub.id, {
              items: [
                {
                  id: (await stripe.subscriptions.retrieve(sub.id)).items
                    .data[0].id,
                  price: newPrice.id,
                },
              ],
            });

            // Update local state
            const subIndex = stripeState.subscriptions.findIndex(
              s => s.id === sub.id
            );
            if (subIndex !== -1) {
              stripeState.subscriptions[subIndex].currentPriceId = newPrice.id;
            }
          }
        } catch (error) {
          console.error(`Failed to downgrade subscription ${sub.id}:`, error);
        }
      }

      // 6. Advance all test clocks to current simulation time
      console.log(
        `Advancing ${stripeState.testClocks.length} test clocks to ${new Date(currentTime * 1000)}`
      );

      for (const testClockId of stripeState.testClocks) {
        try {
          await stripe.testHelpers.testClocks.advance(testClockId, {
            frozen_time: currentTime,
          });
        } catch (error) {
          console.error(`Failed to advance test clock ${testClockId}:`, error);
        }
      }

      currentTime += biweekInSeconds;
      stepCount++;
    }

    const finalActiveSubscriptions = stripeState.subscriptions.filter(
      s => s.active
    ).length;
    const totalCustomers = stripeState.customers.length;
    const totalCancellations = stripeState.canceledSubscriptions.length;

    console.log(`\n=== Simulation Complete ===`);
    console.log(`Total customers: ${totalCustomers}`);
    console.log(`Active subscriptions: ${finalActiveSubscriptions}`);
    console.log(`Canceled subscriptions: ${totalCancellations}`);
    console.log(`Steps completed: ${stepCount}`);

    return {
      success: true,
      initialCustomers: inc,
      finalCustomers: totalCustomers,
      finalActiveSubscriptions: finalActiveSubscriptions,
      totalCancellations,
      stepsCompleted: stepCount,
      productsCreated: 2,
      pricesCreated: prices.length,
      testClocksCreated: stripeState.testClocks.length,
      startDate: new Date(startTimestamp * 1000),
      endDate: new Date(currentTime * 1000),
      stripeState,
    };
  }
);

// Helper function to generate realistic customer data
function generateCustomerData(index: string) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const randomIndex = Math.floor(Math.random() * firstNames.length);

  const firstName = firstNames[randomIndex];
  const lastName =
    lastNames[Math.floor(randomIndex / firstNames.length) % lastNames.length];
  const domain = domains[randomIndex % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
