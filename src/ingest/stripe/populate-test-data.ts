/**
 * Comprehensive Stripe Test Data Ingestion Script (v3)
 *
 * This script is designed to populate a Stripe test account with realistic,
 * time-based subscription data for backfilling and analytics testing.
 *
 * Key Features:
 * - SAFETY CHECKS: Prevents execution on live or production environments.
 * - Cleanup: Deletes existing test data to ensure a clean slate.
 * - Test Clocks: Uses Stripe Test Clocks to simulate the passage of time
 * for accurate event generation (invoices, subscriptions, webhooks).
 * - Historical Simulation: Starts the simulation at the test clock's
 * frozen time, allowing for the creation of historical data.
 * - Lifecycle Events: Simulates customer sign-ups, churn, upgrades,
 * downgrades, and refunds over time.
 * - Pagination: Correctly handles pagination when cleaning up existing
 * resources to ensure all data is removed.
 *
 * Usage:
 * - Ensure your environment variables are set: STRIPE_SECRET_KEY, etc.
 * - Run the script with an Inngest event: `stripe/populate.test.data`
 *
 * Example Event Data:
 * {
 * "testClockId": "clock_1Oq4p4RjW2XmC61y0Zk8j3zF",
 * "initialNumberOfCustomers": 50,
 * "orgIntegrationId": "your_integration_id"
 * }
 */

import Stripe from 'stripe';
import { chunk } from '../../lib/utils';
import { inngest } from '../client';

// configurable
// number of clients
// number of weeks starting (today and backwards)
// weekly churn rate lower upper
// weekly growth rate lower upper
// weekly upgrade rate lower upper
// weekly downgrade rate lower upper
// integration id if passed, if not passed, use the main org integration

// avergae simu costs, cleanup, not a step, can be repeated, i dont care
// number of customers created
// numuber of subsc created
// number of products ? we can keep them the same? or clean them up too and recreate in the first step?
// number of upgrades downgrades is a step
// cancellations are first
// we need to add reactivations too?
// can we do it under 5 min if we move time forward in parralel?
// 1 second check for clocks instead of 2000

interface IngestTestDataParams {
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
  numberOfSteps: number;
  churnRateLower: number;
  churnRateUpper: number;
  growthRateLower: number;
  growthRateUpper: number;
  upgradeRateLower: number;
  upgradeRateUpper: number;
  downgradeRateLower: number;
  downgradeRateUpper: number;
  reactivationRateLower: number;
  reactivationRateUpper: number;
}

// Helper functions for simulation
function getRandomRate(lower: number, upper: number): number {
  return Math.random() * (upper - lower) + lower;
}

function getRandomSubset<T>(array: T[], percentage: number): T[] {
  const count = Math.floor(array.length * (percentage / 100));
  const shuffled = [...array].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}

function getRandomPrice(prices: Stripe.Price[]): Stripe.Price {
  return prices[Math.floor(Math.random() * prices.length)];
}

// TODO throttle it to 1 at the time, no concurrency
export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const {
      initialNumberOfCustomers: inc,
      numberOfSteps,
      churnRateLower,
      churnRateUpper,
      growthRateLower,
      growthRateUpper,
      upgradeRateLower,
      upgradeRateUpper,
      downgradeRateLower,
      downgradeRateUpper,
      reactivationRateLower,
      reactivationRateUpper,
    } = event.data as IngestTestDataParams;

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    console.log('Creating products...');

    // Create all the products mentioned in the code
    const basicProduct = await stripe.products.create({
      name: 'Basic Plan',
      description: 'Basic SaaS plan with essential features',
    });

    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Professional SaaS plan with advanced features',
    });

    const pricingConfig = [
      { product: basicProduct.id, interval: 'month', amount: 3600 },
      { product: basicProduct.id, interval: 'year', amount: 36000 },
      { product: proProduct.id, interval: 'month', amount: 6000 },
      { product: proProduct.id, interval: 'year', amount: 60000 },
    ];

    console.log('Creating prices...');

    const prices = await Promise.all(
      pricingConfig.map(config =>
        stripe.prices.create({
          product: config.product,
          unit_amount: config.amount,
          currency: 'usd',
          recurring: {
            interval: config.interval as 'month' | 'year',
          },
        })
      )
    );

    // Find start date based on numberOfWeeks
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - numberOfSteps * 7);
    const startTimestamp = Math.floor(startDate.getTime() / 1000);

    const createTestCustomer = async () => {
      console.time('createTestCustomer');
      const uid = crypto.randomUUID();
      const newTestClock = await stripe.testHelpers.testClocks.create({
        frozen_time: startTimestamp,
        name: `Test Clock for Customer ${uid}`,
      });
      const customerData = generateCustomerData(uid);
      const customer = await stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        description: `Test customer ${uid}`,
        test_clock: newTestClock.id,
      });
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: { token: 'tok_visa' },
      });
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customer.id,
      });
      await stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: paymentMethod.id,
        },
      });
      const randomPrice = prices[Math.floor(Math.random() * prices.length)];
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: randomPrice.id }],
        collection_method: 'charge_automatically',
        default_payment_method: paymentMethod.id,
      });
      console.timeEnd('createTestCustomer');
      return {
        customerId: customer.id,
        subscriptionId: subscription.id,
        testClockId: newTestClock.id,
        productId:
          typeof randomPrice.product === 'string'
            ? randomPrice.product
            : randomPrice.product.id,
        priceId: randomPrice.id,
      };
    };

    type StripeItemState = {
      customerId: string;
      subscriptionId: string | null;
      testClockId: string;
      productId: string;
      priceId: string;
    };

    // array of objects, customer, subscription, ...
    let stripeState: StripeItemState[] = [];

    for (const c of chunk(Array.from({ length: inc }), 5)) {
      console.time('createTestCustomerBatch');
      const batchResults = await Promise.all(c.map(createTestCustomer));
      stripeState.push(...batchResults);
      console.timeEnd('createTestCustomerBatch');
    }

    // Simulation logic - process each biweekly step
    let currentTime = startTimestamp;
    const endTime = Math.floor(Date.now() / 1000);
    const biweekInSeconds = 14 * 24 * 60 * 60; // 2 weeks in seconds
    let stepCount = 0;

    console.log(
      `Starting simulation from ${new Date(currentTime * 1000)} to ${new Date(endTime * 1000)}`
    );

    while (currentTime < endTime && stepCount < numberOfSteps) {
      console.log(`Step ${stepCount + 1}/${numberOfSteps}`);
      console.time('step');

      const activeCustomers = stripeState.filter(s => s.subscriptionId);

      const churnRate = getRandomRate(churnRateLower, churnRateUpper);

      const subToCancel = getRandomSubset(activeCustomers, churnRate);

      console.log(
        `cancel  ${subToCancel.length} subs: (${churnRate.toFixed(2)}%)`
      );

      // Batch cancel subscriptions
      const cancelBatches = chunk(subToCancel, 5);
      const canceledSubscriptions: string[] = [];

      for (const batch of cancelBatches) {
        await Promise.all(
          batch.map(sub => stripe.subscriptions.cancel(sub.subscriptionId!))
        );
        canceledSubscriptions.push(...batch.map(s => s.subscriptionId!));
      }

      // 2. Process reactivations
      const reactivationRate = getRandomRate(
        reactivationRateLower,
        reactivationRateUpper
      );
      const customersWithoutSubscription = stripeState.filter(
        c => !c.subscriptionId
      );
      const customersToReactivate = getRandomSubset(
        customersWithoutSubscription,
        reactivationRate
      );

      console.log(
        `Reactivating ${customersToReactivate.length} customers (${reactivationRate.toFixed(2)}% reactivation rate)`
      );

      // Batch reactivate customers
      const reactivateBatches = chunk(customersToReactivate, 5);
      const reactivatedSubscriptions = [];

      for (const batch of reactivateBatches) {
        const subscriptions = await Promise.all(
          batch.map(customer => {
            const randomPrice = getRandomPrice(prices);
            return stripe.subscriptions
              .create({
                customer: customer.customerId,
                items: [{ price: randomPrice.id }],
                collection_method: 'charge_automatically',
              })
              .then(subscription => ({ subscription, customer, randomPrice }));
          })
        );
        reactivatedSubscriptions.push(...subscriptions);
      }

      // 3. Process growth (new customers)
      const growthRate = getRandomRate(growthRateLower, growthRateUpper);
      const currentActiveCustomers = stripeState.filter(
        c => c.subscriptionId
      ).length;
      const newCustomersCount = Math.floor(
        currentActiveCustomers * (growthRate / 100)
      );

      console.log(
        `Adding ${newCustomersCount} new customers (${growthRate.toFixed(2)}% growth rate)`
      );

      // Batch create new customers
      const newCustomerBatches = chunk(
        Array.from({ length: newCustomersCount }),
        5
      );
      const newCustomersData = [];

      for (const batch of newCustomerBatches) {
        const customerResults = await Promise.all(
          batch.map(createTestCustomer)
        );
        newCustomersData.push(...customerResults);
      }

      // 4. Process upgrades
      const upgradeRate = getRandomRate(upgradeRateLower, upgradeRateUpper);
      const currentActiveSubscriptions = stripeState.filter(
        s =>
          s.subscriptionId && !canceledSubscriptions.includes(s.subscriptionId)
      );
      const subscriptionsThatCanBeUpgraded = currentActiveSubscriptions.filter(
        s => s.priceId !== proProduct.id
      );
      const subscriptionsThatCanBeDowngraded =
        currentActiveSubscriptions.filter(s => s.priceId === proProduct.id);
      const subscriptionsToUpgrade = getRandomSubset(
        subscriptionsThatCanBeUpgraded,
        upgradeRate
      );

      console.log(
        `Upgrading ${subscriptionsToUpgrade.length} subscriptions (${upgradeRate.toFixed(2)}% upgrade rate)`
      );

      // Batch upgrade subscriptions
      const upgradeBatches = chunk(subscriptionsToUpgrade, 5);
      const modifiedSubscriptions: StripeItemState[] = [];

      for (const batch of upgradeBatches) {
        const upgradeResults = await Promise.all(
          batch.map(async item => {
            const subscription = await stripe.subscriptions.retrieve(
              item.subscriptionId!
            );
            const randomProPrice = getRandomPrice(
              prices.filter(p => p.product === proProduct.id)
            );
            await stripe.subscriptions.update(item.subscriptionId!, {
              items: [
                {
                  id: subscription.items.data[0].id,
                  price: randomProPrice.id,
                },
              ],
            });
            return {
              ...item,
              priceId: randomProPrice.id,
              productId: proProduct.id,
            };
          })
        );
        modifiedSubscriptions.push(...upgradeResults);
      }

      // 5. Process downgrades
      const downgradeRate = getRandomRate(
        downgradeRateLower,
        downgradeRateUpper
      );
      const subscriptionsToDowngrade = getRandomSubset(
        subscriptionsThatCanBeDowngraded,
        downgradeRate
      );

      console.log(
        `Downgrading ${subscriptionsToDowngrade.length} subscriptions (${downgradeRate.toFixed(2)}% downgrade rate)`
      );

      // Batch downgrade subscriptions
      const downgradeBatches = chunk(subscriptionsToDowngrade, 5);

      for (const batch of downgradeBatches) {
        const downgradeResults = await Promise.all(
          batch.map(async sub => {
            const subscription = await stripe.subscriptions.retrieve(
              sub.subscriptionId!
            );
            const randomBasicPrice = getRandomPrice(
              prices.filter(p => p.product === basicProduct.id)
            );
            await stripe.subscriptions.update(sub.subscriptionId!, {
              items: [
                {
                  id: subscription.items.data[0].id,
                  price: randomBasicPrice.id,
                },
              ],
            });
            return {
              ...sub,
              priceId: randomBasicPrice.id,
              productId: basicProduct.id,
            };
          })
        );
        modifiedSubscriptions.push(...downgradeResults);
      }

      // 6. Update stripe state after all operations are complete
      console.log('Updating local stripe state...');

      stripeState = stripeState.map(itemInState => {
        if (canceledSubscriptions.includes(itemInState.subscriptionId || '')) {
          return {
            ...itemInState,
            subscriptionId: null,
          };
        }
        const modifiedItem = modifiedSubscriptions.find(
          s => s.subscriptionId === itemInState.subscriptionId
        );

        return modifiedItem || itemInState;
      });

      const testClockBatches = chunk(
        stripeState.map(s => s.testClockId),
        5
      );
      for (const batch of testClockBatches) {
        await Promise.all(
          batch.map(testClockId =>
            stripe.testHelpers.testClocks.advance(testClockId, {
              frozen_time: currentTime,
            })
          )
        );
      }

      currentTime += biweekInSeconds;
      stepCount++;
      console.time('step');
    }
  }
);

// Helper function to generate realistic customer data
function generateCustomerData(index: string) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const randomIndex = Math.floor(Math.random() * firstNames.length);

  const firstName = firstNames[randomIndex];
  const lastName =
    lastNames[Math.floor(randomIndex / firstNames.length) % lastNames.length];
  const domain = domains[randomIndex % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
