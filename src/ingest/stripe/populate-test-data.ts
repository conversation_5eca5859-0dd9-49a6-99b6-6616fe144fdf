/**
 * Comprehensive Stripe Test Data Ingestion Script (v3)
 *
 * This script is designed to populate a Stripe test account with realistic,
 * time-based subscription data for backfilling and analytics testing.
 *
 * Key Features:
 * - SAFETY CHECKS: Prevents execution on live or production environments.
 * - Cleanup: Deletes existing test data to ensure a clean slate.
 * - Test Clocks: Uses Stripe Test Clocks to simulate the passage of time
 * for accurate event generation (invoices, subscriptions, webhooks).
 * - Historical Simulation: Starts the simulation at the test clock's
 * frozen time, allowing for the creation of historical data.
 * - Lifecycle Events: Simulates customer sign-ups, churn, upgrades,
 * downgrades, and refunds over time.
 * - Pagination: Correctly handles pagination when cleaning up existing
 * resources to ensure all data is removed.
 *
 * Usage:
 * - Ensure your environment variables are set: STRIPE_SECRET_KEY, etc.
 * - Run the script with an Inngest event: `stripe/populate.test.data`
 *
 * Example Event Data:
 * {
 * "testClockId": "clock_1Oq4p4RjW2XmC61y0Zk8j3zF",
 * "initialNumberOfCustomers": 50,
 * "orgIntegrationId": "your_integration_id"
 * }
 */

import Stripe from 'stripe';
import { chunk } from '../../lib/utils';
import { inngest } from '../client';

// configurable
// number of clients
// number of weeks starting (today and backwards)
// weekly churn rate lower upper
// weekly growth rate lower upper
// weekly upgrade rate lower upper
// weekly downgrade rate lower upper
// integration id if passed, if not passed, use the main org integration

// avergae simu costs, cleanup, not a step, can be repeated, i dont care
// number of customers created
// numuber of subsc created
// number of products ? we can keep them the same? or clean them up too and recreate in the first step?
// number of upgrades downgrades is a step
// cancellations are first
// we need to add reactivations too?
// can we do it under 5 min if we move time forward in parralel?
// 1 second check for clocks instead of 2000

interface IngestTestDataParams {
  initialNumberOfCustomers: number;
  orgIntegrationId: string;
  numberOfSteps: number;
  churnRateLower: number;
  churnRateUpper: number;
  growthRateLower: number;
  growthRateUpper: number;
  upgradeRateLower: number;
  upgradeRateUpper: number;
  downgradeRateLower: number;
  downgradeRateUpper: number;
  reactivationRateLower: number;
  reactivationRateUpper: number;
}

// TODO throttle it to 1 at the time, no concurrency
export const populateStripeTestData = inngest.createFunction(
  { id: 'ingest-test-data' },
  { event: 'stripe/populate.test.data' },
  async ({ event }) => {
    const { initialNumberOfCustomers: inc, numberOfSteps } =
      event.data as IngestTestDataParams;

    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2025-08-27.basil',
    });

    c

    // Create all the products mentioned in the code
    const basicProduct = await stripe.products.create({
      name: 'Basic Plan',
      description: 'Basic SaaS plan with essential features',
    });

    const proProduct = await stripe.products.create({
      name: 'Pro Plan',
      description: 'Professional SaaS plan with advanced features',
    });

    const pricingConfig = [
      { product: basicProduct.id, interval: 'month', amount: 3600 },
      { product: basicProduct.id, interval: 'year', amount: 36000 },
      { product: proProduct.id, interval: 'month', amount: 6000 },
      { product: proProduct.id, interval: 'year', amount: 60000 },
    ];

    const prices = await Promise.all(
      pricingConfig.map(config =>
        stripe.prices.create({
          product: config.product,
          unit_amount: config.amount,
          currency: 'usd',
          recurring: {
            interval: config.interval as 'month' | 'year',
          },
        })
      )
    );

    // Find start date based on numberOfWeeks
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - numberOfSteps * 7);
    const startTimestamp = Math.floor(startDate.getTime() / 1000);

    const createTestCustomer = async () => {
      console.time('createTestCustomer');
      const uid = crypto.randomUUID();
      const newTestClock = await stripe.testHelpers.testClocks.create({
        frozen_time: startTimestamp,
        name: `Test Clock for Customer ${uid}`,
      });
      const customerData = generateCustomerData(uid);
      const customer = await stripe.customers.create({
        email: customerData.email,
        name: customerData.name,
        description: `Test customer ${uid}`,
        test_clock: newTestClock.id,
      });
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: { token: 'tok_visa' },
      });
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customer.id,
      });
      await stripe.customers.update(customer.id, {
        invoice_settings: {
          default_payment_method: paymentMethod.id,
        },
      });
      const randomPrice = prices[Math.floor(Math.random() * prices.length)];
      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{ price: randomPrice.id }],
        collection_method: 'charge_automatically',
        default_payment_method: paymentMethod.id,
      });
      console.timeEnd('createTestCustomer');
      return { customer, subscription, testClock: newTestClock.id };
    };

    const customers = [];
    const subscriptions = [];
    const testClocks = [];

    for (const c of chunk(Array.from({ length: inc }), 5)) {
      console.time('createTestCustomerBatch');
      const batchResults = await Promise.all(c.map(createTestCustomer));
      customers.push(...batchResults.map(r => r.customer));
      subscriptions.push(...batchResults.map(r => r.subscription));
      testClocks.push(...batchResults.map(r => r.testClock));
      console.timeEnd('createTestCustomerBatch');
    }

    // Keep local copy of stripe state
    const stripeState = {
      customers: customers.map(c => ({ id: c.id, hasSubscription: true })),
      subscriptions: subscriptions.map(s => ({ id: s.id, active: true })),
      testClocks,
      prices,
    };

    // Empty while loop for simulation (to be filled in next step)
    let currentTime = startTimestamp;
    const endTime = Math.floor(Date.now() / 1000);
    const weekInSeconds = 7 * 24 * 60 * 60;

    while (currentTime < endTime) {
      // Empty for now - simulation logic will be added in next step
      currentTime += weekInSeconds;
    }

    return {
      success: true,
      initialCustomers: inc,
      productsCreated: 2,
      pricesCreated: prices.length,
      testClocksCreated: testClocks.length,
      startDate: new Date(startTimestamp * 1000),
      stripeState,
    };
  }
);

// Helper function to generate realistic customer data
function generateCustomerData(index: string) {
  const firstNames = [
    'John',
    'Jane',
    'Mike',
    'Sarah',
    'David',
    'Lisa',
    'Chris',
    'Emma',
    'Alex',
    'Maria',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'company.com',
  ];

  const randomIndex = Math.floor(Math.random() * firstNames.length);

  const firstName = firstNames[randomIndex];
  const lastName =
    lastNames[Math.floor(randomIndex / firstNames.length) % lastNames.length];
  const domain = domains[randomIndex % domains.length];

  return {
    name: `${firstName} ${lastName}`,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${index}@${domain}`,
  };
}
