export const chunk = <T>(arr: T[], size: number): T[][] => {
  const result: T[][] = [];
  for (let i = 0; i < arr.length; i += size) {
    result.push(arr.slice(i, i + size));
  }
  return result;
};

export const paginateAndProcess = async <T extends { id: string }>(
  fetchPage: (cursor?: string) => Promise<{ data: T[]; has_more: boolean }>,
  processItem: (itemId: string) => Promise<unknown>,
  processBatchSize: number = 5
): Promise<void> => {
  console.time('paginateAndProcess');
  let hasMore = true;
  let lastId: string | null = null;

  while (hasMore) {
    const page = await fetchPage(lastId || undefined);

    const chunks = chunk(page.data, processBatchSize);
    for (const chunk of chunks) {
      await Promise.all(chunk.map(item => processItem(item.id)));
    }

    lastId = page.data.at(-1)?.id || null;
    hasMore = page.has_more;
  }
  console.timeEnd('paginateAndProcess');
};
