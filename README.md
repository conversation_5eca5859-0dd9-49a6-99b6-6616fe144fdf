# Alden - Stripe Test Data Population

## Overview

This project contains a Stripe test data population system designed to create realistic SaaS subscription data for testing and analytics purposes.

## Recent Changes

### Stripe Test Data Population Rewrite (2025-01-11)

The `populateStripeTestData` function in `src/ingest/stripe/populate-test-data.ts` has been completely rewritten to be more minimal and efficient:

#### Key Changes Made:

1. **Simplified Structure**: Removed error handling, logging, and complex retry logic for a cleaner, minimal implementation
2. **Cleanup Process**:
   - Clean up all existing products by setting them to inactive
   - Clean up all existing test clocks (which automatically cleans up associated customers and subscriptions)
3. **Product Creation**: Creates Basic and Pro plan products with monthly/yearly pricing:
   - Basic Plan: $29.99/month, $299.99/year
   - Pro Plan: $59.99/month, $599.99/year
4. **Customer Creation**: Uses Promise.all with chunking by 5 for efficient parallel customer creation
5. **Date Calculation**: Calculates start date based on `numberOfWeeks` parameter (going backwards from today)
6. **State Management**: Maintains local copy of Stripe state to track customer subscription status
7. **Simulation Framework**: Empty while loop prepared for future simulation logic

#### Function Parameters:

- `initialNumberOfCustomers`: Number of customers to create initially
- `numberOfWeeks`: Number of weeks to go back from today for the start date

#### Return Value:

Returns an object containing:

- `success`: Boolean indicating successful completion
- `initialCustomers`: Number of customers created
- `productsCreated`: Number of products created (always 2)
- `pricesCreated`: Number of price objects created (4 total)
- `testClocksCreated`: Number of test clocks created
- `startDate`: The calculated start date for simulation
- `stripeState`: Local copy of Stripe state for tracking

#### Next Steps:

The while loop is currently empty and ready for the next phase of development where simulation logic will be added to handle:

- Customer churn
- Subscription upgrades/downgrades
- New customer acquisition over time
- Revenue tracking

## Usage

The function is designed to be called as an Inngest function with the event `stripe/populate.test.data` and appropriate parameters.
